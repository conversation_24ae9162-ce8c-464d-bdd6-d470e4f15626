<?xml version="1.0" encoding="utf-8"?>
<data noupdate="1">
    
    <!-- Partner Categories -->
    <record id="res_partner_category_customer" model="res.partner.category">
        <field name="name">Customer</field>
        <field name="color">1</field>
    </record>
    
    <record id="res_partner_category_supplier" model="res.partner.category">
        <field name="name">Supplier</field>
        <field name="color">2</field>
    </record>
    
    <record id="res_partner_category_employee" model="res.partner.category">
        <field name="name">Employee</field>
        <field name="color">3</field>
    </record>
    
    <!-- Partner Titles -->
    <record id="res_partner_title_mister" model="res.partner.title">
        <field name="name">Mr.</field>
        <field name="shortcut">Mr.</field>
    </record>
    
    <record id="res_partner_title_madam" model="res.partner.title">
        <field name="name">Mrs.</field>
        <field name="shortcut">Mrs.</field>
    </record>
    
    <record id="res_partner_title_miss" model="res.partner.title">
        <field name="name">Miss</field>
        <field name="shortcut">Miss</field>
    </record>
    
    <record id="res_partner_title_doctor" model="res.partner.title">
        <field name="name">Doctor</field>
        <field name="shortcut">Dr.</field>
    </record>
    
    <!-- Countries (basic set) -->
    <record id="country_us" model="res.country">
        <field name="name">United States</field>
        <field name="code">US</field>
    </record>
    
    <record id="country_ca" model="res.country">
        <field name="name">Canada</field>
        <field name="code">CA</field>
    </record>
    
    <record id="country_gb" model="res.country">
        <field name="name">United Kingdom</field>
        <field name="code">GB</field>
    </record>
    
    <record id="country_fr" model="res.country">
        <field name="name">France</field>
        <field name="code">FR</field>
    </record>
    
    <record id="country_de" model="res.country">
        <field name="name">Germany</field>
        <field name="code">DE</field>
    </record>
    
    <!-- Default Company Partner -->
    <record id="main_company" model="res.partner">
        <field name="name">My Company</field>
        <field name="is_company">True</field>
        <field name="email"><EMAIL></field>
        <field name="phone">******-123-4567</field>
        <field name="website">https://www.mycompany.com</field>
        <field name="street">123 Business Street</field>
        <field name="city">Business City</field>
        <field name="zip">12345</field>
        <field name="country_id" ref="country_us"/>
        <field name="customer_rank">0</field>
        <field name="supplier_rank">0</field>
        <field name="lang">en_US</field>
        <field name="tz">UTC</field>
        <field name="comment">Default company created during system initialization</field>
    </record>
    
    <!-- System Administrator Partner -->
    <record id="partner_admin" model="res.partner">
        <field name="name">Administrator</field>
        <field name="is_company">False</field>
        <field name="email"><EMAIL></field>
        <field name="phone">******-123-4567</field>
        <field name="parent_id" ref="main_company"/>
        <field name="function">System Administrator</field>
        <field name="title" ref="res_partner_title_mister"/>
        <field name="category_id" ref="res_partner_category_employee"/>
        <field name="lang">en_US</field>
        <field name="tz">UTC</field>
        <field name="comment">System administrator account</field>
    </record>
    
    <!-- Demo User Partner -->
    <record id="partner_demo" model="res.partner">
        <field name="name">Demo User</field>
        <field name="is_company">False</field>
        <field name="email"><EMAIL></field>
        <field name="phone">******-123-4568</field>
        <field name="parent_id" ref="main_company"/>
        <field name="function">Demo User</field>
        <field name="title" ref="res_partner_title_mister"/>
        <field name="category_id" ref="res_partner_category_employee"/>
        <field name="lang">en_US</field>
        <field name="tz">UTC</field>
        <field name="comment">Demo user account for testing</field>
    </record>
    
    <!-- Portal User Partner -->
    <record id="partner_portal" model="res.partner">
        <field name="name">Portal User</field>
        <field name="is_company">False</field>
        <field name="email"><EMAIL></field>
        <field name="phone">******-123-4569</field>
        <field name="function">External User</field>
        <field name="title" ref="res_partner_title_mister"/>
        <field name="customer_rank">1</field>
        <field name="lang">en_US</field>
        <field name="tz">UTC</field>
        <field name="comment">Portal user with limited access</field>
    </record>
    
</data>
