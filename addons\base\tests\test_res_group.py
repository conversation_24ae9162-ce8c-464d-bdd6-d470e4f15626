"""
Test suite for res.groups model
"""

import pytest
from unittest.mock import AsyncMock, patch

from erp.models.base import BaseModel
from erp.fields import FieldValidationError


class TestResGroup:
    """Test res.groups model functionality"""
    
    @pytest.fixture
    async def group_model(self):
        """Create a group model instance for testing"""
        from addons.base.models.res_group import ResGroup
        return ResGroup
    
    @pytest.fixture
    def sample_group_data(self):
        """Sample group data for testing"""
        return {
            'name': 'Test Group',
            'comment': 'Test group for unit testing',
            'sequence': 10,
            'active': True,
            'share': False
        }
    
    def test_group_model_attributes(self, group_model):
        """Test group model has correct attributes"""
        assert group_model._name == 'res.groups'
        assert group_model._description == 'Access Groups'
        assert group_model._table == 'res_groups'
        
        # Check that required fields exist
        fields = group_model._fields
        assert 'name' in fields
        assert 'category_id' in fields
        assert 'users' in fields
        assert 'implied_ids' in fields
        assert 'comment' in fields
        assert 'share' in fields
    
    def test_group_initialization(self, group_model, sample_group_data):
        """Test group initialization with data"""
        group = group_model(**sample_group_data)
        
        assert group.name == 'Test Group'
        assert group.comment == 'Test group for unit testing'
        assert group.sequence == 10
        assert group.active is True
        assert group.share is False
    
    def test_full_name_computation(self, group_model):
        """Test full name computation with and without category"""
        # Test group without category
        group = group_model(name='Test Group')
        group._compute_full_name()
        assert group.full_name == 'Test Group'
        
        # Test group with category (mock category)
        from unittest.mock import MagicMock
        category = MagicMock()
        category.name = 'Administration'
        
        group_with_category = group_model(name='System Admin')
        group_with_category.category_id = category
        group_with_category._compute_full_name()
        assert group_with_category.full_name == 'Administration / System Admin'
    
    async def test_get_implied_groups(self, group_model):
        """Test getting implied groups recursively"""
        # Create mock groups
        base_group = group_model(name='Base Group')
        base_group.id = 'base_id'
        base_group.implied_ids = []
        
        admin_group = group_model(name='Admin Group')
        admin_group.id = 'admin_id'
        admin_group.implied_ids = [base_group]
        
        super_admin_group = group_model(name='Super Admin')
        super_admin_group.id = 'super_admin_id'
        super_admin_group.implied_ids = [admin_group]
        
        # Test implied groups collection
        implied = await super_admin_group.get_implied_groups()
        assert 'admin_id' in implied
        assert 'base_id' in implied
    
    def test_required_fields(self, group_model):
        """Test that required fields are enforced"""
        # Name is required
        with pytest.raises(FieldValidationError):
            group = group_model()
            group._fields['name'].validate(None)
    
    def test_default_values(self, group_model):
        """Test default field values"""
        group = group_model(name='Test Group')
        
        # Check default values
        assert group.sequence == 10
        assert group.active is True
        assert group.share is False
        assert group.color == 0


class TestIrModuleCategory:
    """Test ir.module.category model"""
    
    @pytest.fixture
    async def category_model(self):
        """Create a category model instance for testing"""
        from addons.base.models.res_group import IrModuleCategory
        return IrModuleCategory
    
    @pytest.fixture
    def sample_category_data(self):
        """Sample category data for testing"""
        return {
            'name': 'Administration',
            'description': 'Administration and system management',
            'sequence': 1,
            'visible': True,
            'exclusive': False
        }
    
    def test_category_model_attributes(self, category_model):
        """Test category model has correct attributes"""
        assert category_model._name == 'ir.module.category'
        assert category_model._description == 'Application'
        assert category_model._table == 'ir_module_category'
        
        # Check that required fields exist
        fields = category_model._fields
        assert 'name' in fields
        assert 'description' in fields
        assert 'sequence' in fields
        assert 'parent_id' in fields
        assert 'child_ids' in fields
        assert 'group_ids' in fields
        assert 'visible' in fields
        assert 'exclusive' in fields
    
    def test_category_initialization(self, category_model, sample_category_data):
        """Test category initialization with data"""
        category = category_model(**sample_category_data)
        
        assert category.name == 'Administration'
        assert category.description == 'Administration and system management'
        assert category.sequence == 1
        assert category.visible is True
        assert category.exclusive is False
    
    async def test_name_get_with_hierarchy(self, category_model):
        """Test name_get method with parent hierarchy"""
        # Create parent category
        parent = category_model(name='System')
        parent.id = 'parent_id'
        parent.parent_id = None
        
        # Create child category
        child = category_model(name='Administration')
        child.id = 'child_id'
        child.parent_id = parent
        
        # Test name_get for hierarchical display
        result = await child.name_get()
        expected_name = 'System / Administration'
        assert result == [('child_id', expected_name)]
    
    def test_default_values(self, category_model):
        """Test default field values"""
        category = category_model(name='Test Category')
        
        # Check default values
        assert category.sequence == 10
        assert category.visible is True
        assert category.exclusive is False
    
    def test_required_fields(self, category_model):
        """Test that required fields are enforced"""
        # Name is required
        with pytest.raises(FieldValidationError):
            category = category_model()
            category._fields['name'].validate(None)


class TestGroupsIntegration:
    """Test integration between groups and categories"""
    
    @pytest.fixture
    async def models(self):
        """Get both group and category models"""
        from addons.base.models.res_group import ResGroup, IrModuleCategory
        return ResGroup, IrModuleCategory
    
    def test_group_category_relationship(self, models):
        """Test relationship between groups and categories"""
        ResGroup, IrModuleCategory = models
        
        # Create category
        category = IrModuleCategory(name='Administration', sequence=1)
        category.id = 'admin_category'
        
        # Create group with category
        group = ResGroup(name='System Admin')
        group.category_id = category
        
        # Test full name computation
        group._compute_full_name()
        assert group.full_name == 'Administration / System Admin'
    
    async def test_group_hierarchy(self, models):
        """Test group hierarchy with implied groups"""
        ResGroup, IrModuleCategory = models
        
        # Create base group
        user_group = ResGroup(name='User')
        user_group.id = 'user_group'
        user_group.implied_ids = []
        
        # Create admin group that implies user group
        admin_group = ResGroup(name='Administrator')
        admin_group.id = 'admin_group'
        admin_group.implied_ids = [user_group]
        
        # Test that admin group implies user group
        implied = await admin_group.get_implied_groups()
        assert 'user_group' in implied
