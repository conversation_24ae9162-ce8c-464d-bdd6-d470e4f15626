"""
Partner model - Core contact and company management

This model represents partners (contacts, customers, suppliers, companies)
in the ERP system, similar to Odoo's res.partner model.
"""

from erp.models.base import BaseModel
from erp import fields


class ResPartner(BaseModel):
    """Partner model for contacts, customers, suppliers, and companies"""
    
    _name = 'res.partner'
    _description = 'Partner'
    _table = 'res_partner'
    
    # Override name field to be more specific for partners
    name = fields.Char(string='Name', required=True, index=True, size=100,
                help='Name of the partner (person or company)')

    # Contact information
    email = fields.Char(string='Email', size=254, help='Email address')
    phone = fields.Char(string='Phone', size=32, help='Phone number')
    mobile = fields.Char(string='Mobile', size=32, help='Mobile phone number')
    website = fields.Char(string='Website', size=128, help='Website URL')

    # Address fields
    street = fields.Char(string='Street', size=128, help='Street address')
    street2 = fields.Char(string='Street 2', size=128, help='Additional street address')
    city = fields.Char(string='City', size=64, help='City')
    state_id = fields.Many2One('res.country.state', string='State',
                       help='State or province')
    zip = fields.Char(string='ZIP', size=24, help='ZIP or postal code')
    country_id = fields.Many2One('res.country', string='Country', help='Country')

    # Partner type and classification
    is_company = fields.Boolean(string='Is a Company', default=False,
                        help='Check if the partner is a company, otherwise it is a person')
    company_type = fields.Selection([
        ('person', 'Individual'),
        ('company', 'Company')
    ], string='Company Type', compute='_compute_company_type', inverse='_inverse_company_type',
       help='Technical field to support legacy code')

    # Hierarchy
    parent_id = fields.Many2One('res.partner', string='Related Company', index=True,
                        help='Parent company (if this partner is a contact)')
    child_ids = fields.One2Many('res.partner', 'parent_id', string='Contacts',
                        help='Child contacts of this partner')

    # Business information
    function = fields.Char(string='Job Position', size=128, help='Job position')
    title = fields.Many2One('res.partner.title', string='Title',
                    help='Title (Mr., Mrs., etc.)')

    # Categories and tags
    category_id = fields.Many2One('res.partner.category', string='Category',
                          help='Partner category for classification')

    # Status and control
    active = fields.Boolean(string='Active', default=True,
                    help='Uncheck to archive the partner')

    # Customer/Supplier flags
    customer_rank = fields.Integer(string='Customer Rank', default=0,
                           help='How many times this partner has been used as customer')
    supplier_rank = fields.Integer(string='Supplier Rank', default=0,
                           help='How many times this partner has been used as supplier')
    
    # Language and localization
    lang = fields.Selection([
        ('en_US', 'English (US)'),
        ('en_GB', 'English (UK)'),
        ('fr_FR', 'French'),
        ('es_ES', 'Spanish'),
        ('de_DE', 'German'),
    ], string='Language', default='en_US', help='Partner language')

    # Timezone
    tz = fields.Selection([
        ('UTC', 'UTC'),
        ('US/Eastern', 'US/Eastern'),
        ('US/Central', 'US/Central'),
        ('US/Mountain', 'US/Mountain'),
        ('US/Pacific', 'US/Pacific'),
        ('Europe/London', 'Europe/London'),
        ('Europe/Paris', 'Europe/Paris'),
        ('Europe/Berlin', 'Europe/Berlin'),
    ], string='Timezone', default='UTC', help='Partner timezone')

    # Comments and notes
    comment = fields.Text(string='Notes', help='Internal notes about this partner')

    # Reference field for external systems
    ref = fields.Char(string='Reference', size=64, help='Internal reference')

    # VAT number for companies
    vat = fields.Char(string='Tax ID', size=32, help='Tax identification number')

    # Commercial fields
    commercial_partner_id = fields.Many2One('res.partner', string='Commercial Entity',
                                   compute='_compute_commercial_partner',
                                   help='The commercial entity that this partner belongs to')

    # Display name computation
    display_name = fields.Char(string='Display Name', compute='_compute_display_name',
                       help='Name to display for this partner')
    
    def _compute_company_type(self):
        """Compute company type based on is_company field"""
        # Handle both single instances and recordsets
        partners = self if hasattr(self, '__iter__') and not isinstance(self, str) else [self]
        for partner in partners:
            partner.company_type = 'company' if partner.is_company else 'person'
    
    def _inverse_company_type(self):
        """Set is_company based on company_type"""
        # Handle both single instances and recordsets
        partners = self if hasattr(self, '__iter__') and not isinstance(self, str) else [self]
        for partner in partners:
            partner.is_company = partner.company_type == 'company'
    
    def _compute_commercial_partner(self):
        """Compute the commercial partner (top-level company)"""
        # Handle both single instances and recordsets
        partners = self if hasattr(self, '__iter__') and not isinstance(self, str) else [self]
        for partner in partners:
            if partner.is_company or not partner.parent_id:
                partner.commercial_partner_id = partner.id
            else:
                # Find the top-level company
                current = partner
                while current.parent_id and not current.parent_id.is_company:
                    current = current.parent_id
                partner.commercial_partner_id = current.parent_id.id if current.parent_id else partner.id
    
    def _compute_display_name(self):
        """Compute display name for the partner"""
        # Handle both single instances and recordsets
        partners = self if hasattr(self, '__iter__') and not isinstance(self, str) else [self]
        for partner in partners:
            name = partner.name or ''
            if partner.parent_id and not partner.is_company:
                name = f"{partner.parent_id.name}, {name}"
            partner.display_name = name
    
    async def name_get(self):
        """Return name representation for this partner"""
        result = []
        for partner in self:
            name = partner.display_name or partner.name or ''
            result.append((partner.id, name))
        return result
    
    async def _name_search(self, name='', domain=None, operator='ilike', limit=100):
        """Search partners by name, email, or reference"""
        if domain is None:
            domain = []
        
        if name:
            # Search in name, email, and ref fields
            name_domain = [
                '|', '|',
                ('name', operator, name),
                ('email', operator, name),
                ('ref', operator, name)
            ]
            domain = name_domain + domain
        
        return await self.search(domain, limit=limit)
    
    async def get_contact_details(self):
        """Get formatted contact details for this partner"""
        details = []
        
        if self.email:
            details.append(f"Email: {self.email}")
        if self.phone:
            details.append(f"Phone: {self.phone}")
        if self.mobile:
            details.append(f"Mobile: {self.mobile}")
        
        # Address
        address_parts = []
        if self.street:
            address_parts.append(self.street)
        if self.street2:
            address_parts.append(self.street2)
        if self.city:
            address_parts.append(self.city)
        if self.zip:
            address_parts.append(self.zip)
        
        if address_parts:
            details.append(f"Address: {', '.join(address_parts)}")
        
        return details


class ResPartnerCategory(BaseModel):
    """Partner categories for classification"""

    _name = 'res.partner.category'
    _description = 'Partner Category'
    _table = 'res_partner_category'

    name = fields.Char(string='Category Name', required=True, translate=True, size=64,
                help='Name of the partner category')

    color = fields.Integer(string='Color Index', default=0,
                   help='Color index for category display')

    parent_id = fields.Many2One('res.partner.category', string='Parent Category',
                        help='Parent category for hierarchical organization')

    child_ids = fields.One2Many('res.partner.category', 'parent_id', string='Child Categories',
                        help='Child categories')

    partner_ids = fields.Many2Many('res.partner', 'res_partner_category_rel', 'category_id', 'partner_id',
                           string='Partners', help='Partners in this category')

    active = fields.Boolean(string='Active', default=True,
                    help='Uncheck to archive the category')


class ResPartnerTitle(BaseModel):
    """Partner titles (Mr., Mrs., Dr., etc.)"""

    _name = 'res.partner.title'
    _description = 'Partner Title'
    _table = 'res_partner_title'

    name = fields.Char(string='Title', required=True, translate=True, size=32,
                help='Full title name')

    shortcut = fields.Char(string='Abbreviation', size=16, translate=True,
                   help='Abbreviated form of the title')


class ResCountry(BaseModel):
    """Countries"""

    _name = 'res.country'
    _description = 'Country'
    _table = 'res_country'

    name = fields.Char(string='Country Name', required=True, translate=True, size=64,
                help='Name of the country')

    code = fields.Char(string='Country Code', required=True, size=2, unique=True,
               help='ISO 3166-1 alpha-2 country code')

    address_format = fields.Text(string='Address Format',
                         help='Format for addresses in this country')

    currency_id = fields.Many2One('res.currency', string='Currency',
                          help='Default currency for this country')

    phone_code = fields.Integer(string='Country Calling Code',
                        help='Country calling code for phone numbers')


class ResCountryState(BaseModel):
    """Country states/provinces"""

    _name = 'res.country.state'
    _description = 'Country State'
    _table = 'res_country_state'

    name = fields.Char(string='State Name', required=True, translate=True, size=64,
                help='Name of the state or province')

    code = fields.Char(string='State Code', required=True, size=3,
               help='State or province code')

    country_id = fields.Many2One('res.country', string='Country', required=True,
                         help='Country this state belongs to')
