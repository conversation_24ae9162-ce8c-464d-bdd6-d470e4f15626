<?xml version="1.0" encoding="utf-8"?>
<data noupdate="1">
    
    <!-- System Administrator User -->
    <record id="user_admin" model="res.users">
        <field name="partner_id" ref="partner_admin"/>
        <field name="login">admin</field>
        <field name="password">admin</field>
        <field name="active">True</field>
        <field name="groups_id" eval="[(6, 0, [ref('group_system'), ref('group_user_admin'), ref('group_partner_manager')])]"/>
        <field name="notification_type">inbox</field>
        <field name="signature">--
Administrator
My Company
<EMAIL></field>
    </record>
    
    <!-- Demo User -->
    <record id="user_demo" model="res.users">
        <field name="partner_id" ref="partner_demo"/>
        <field name="login">demo</field>
        <field name="password">demo</field>
        <field name="active">True</field>
        <field name="groups_id" eval="[(6, 0, [ref('group_user')])]"/>
        <field name="notification_type">email</field>
        <field name="signature">--
Demo User
My Company
<EMAIL></field>
    </record>
    
    <!-- Portal User -->
    <record id="user_portal" model="res.users">
        <field name="partner_id" ref="partner_portal"/>
        <field name="login">portal</field>
        <field name="password">portal</field>
        <field name="active">True</field>
        <field name="groups_id" eval="[(6, 0, [ref('group_portal')])]"/>
        <field name="notification_type">email</field>
        <field name="signature">--
Portal User
<EMAIL></field>
    </record>
    
    <!-- Public Partner for anonymous access -->
    <record id="public_partner" model="res.partner">
        <field name="name">Public User</field>
        <field name="is_company">False</field>
        <field name="email">public@localhost</field>
        <field name="active">True</field>
        <field name="lang">en_US</field>
        <field name="tz">UTC</field>
        <field name="comment">Public user for anonymous access</field>
    </record>

    <!-- Public User (for anonymous access) -->
    <record id="user_public" model="res.users">
        <field name="partner_id" ref="public_partner"/>
        <field name="login">public</field>
        <field name="password" eval="False"/>
        <field name="active">True</field>
        <field name="groups_id" eval="[(6, 0, [ref('group_public')])]"/>
        <field name="notification_type">email</field>
    </record>
    
</data>
