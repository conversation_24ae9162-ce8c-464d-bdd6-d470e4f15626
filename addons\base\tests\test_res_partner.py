"""
Test suite for res.partner model
"""

import pytest
from unittest.mock import AsyncMock, patch

from erp.models.base import BaseModel
from erp.fields import FieldValidationError


class TestResPartner:
    """Test res.partner model functionality"""
    
    @pytest.fixture
    async def partner_model(self):
        """Create a partner model instance for testing"""
        from addons.base.models.res_partner import ResPartner
        return ResPartner
    
    @pytest.fixture
    def sample_partner_data(self):
        """Sample partner data for testing"""
        return {
            'name': 'Test Partner',
            'email': '<EMAIL>',
            'phone': '******-123-4567',
            'is_company': False,
            'street': '123 Test Street',
            'city': 'Test City',
            'zip': '12345',
            'lang': 'en_US',
            'tz': 'UTC'
        }
    
    @pytest.fixture
    def sample_company_data(self):
        """Sample company data for testing"""
        return {
            'name': 'Test Company',
            'email': '<EMAIL>',
            'phone': '******-987-6543',
            'is_company': True,
            'website': 'https://www.testcompany.com',
            'street': '456 Business Ave',
            'city': 'Business City',
            'zip': '54321',
            'vat': 'US123456789'
        }
    
    def test_partner_model_attributes(self, partner_model):
        """Test partner model has correct attributes"""
        assert partner_model._name == 'res.partner'
        assert partner_model._description == 'Partner'
        assert partner_model._table == 'res_partner'
        
        # Check that required fields exist
        fields = partner_model._fields
        assert 'name' in fields
        assert 'email' in fields
        assert 'phone' in fields
        assert 'is_company' in fields
        assert 'parent_id' in fields
        assert 'child_ids' in fields
    
    def test_partner_initialization(self, partner_model, sample_partner_data):
        """Test partner initialization with data"""
        partner = partner_model(**sample_partner_data)
        
        assert partner.name == 'Test Partner'
        assert partner.email == '<EMAIL>'
        assert partner.phone == '******-123-4567'
        assert partner.is_company is False
        assert partner.street == '123 Test Street'
        assert partner.city == 'Test City'
        assert partner.zip == '12345'
    
    def test_company_initialization(self, partner_model, sample_company_data):
        """Test company initialization with data"""
        company = partner_model(**sample_company_data)
        
        assert company.name == 'Test Company'
        assert company.email == '<EMAIL>'
        assert company.is_company is True
        assert company.website == 'https://www.testcompany.com'
        assert company.vat == 'US123456789'
    
    def test_company_type_computation(self, partner_model):
        """Test company_type field computation"""
        # Test person
        person = partner_model(name='John Doe', is_company=False)
        person._compute_company_type()
        assert person.company_type == 'person'
        
        # Test company
        company = partner_model(name='Test Corp', is_company=True)
        company._compute_company_type()
        assert company.company_type == 'company'
    
    def test_company_type_inverse(self, partner_model):
        """Test company_type inverse computation"""
        partner = partner_model(name='Test Partner')
        
        # Set to company
        partner.company_type = 'company'
        partner._inverse_company_type()
        assert partner.is_company is True
        
        # Set to person
        partner.company_type = 'person'
        partner._inverse_company_type()
        assert partner.is_company is False
    
    def test_display_name_computation(self, partner_model):
        """Test display name computation"""
        # Test simple partner
        partner = partner_model(name='John Doe')
        partner._compute_display_name()
        assert partner.display_name == 'John Doe'
        
        # Test partner with parent company
        company = partner_model(name='Test Company', is_company=True)
        contact = partner_model(name='John Doe', parent_id=company, is_company=False)
        contact._compute_display_name()
        assert contact.display_name == 'Test Company, John Doe'
    
    async def test_get_contact_details(self, partner_model, sample_partner_data):
        """Test get_contact_details method"""
        partner = partner_model(**sample_partner_data)
        details = await partner.get_contact_details()
        
        assert 'Email: <EMAIL>' in details
        assert 'Phone: ******-123-4567' in details
        assert 'Address: 123 Test Street, Test City, 12345' in details
    
    def test_required_fields(self, partner_model):
        """Test that required fields are enforced"""
        # Name is required
        with pytest.raises(FieldValidationError):
            partner = partner_model()
            partner._fields['name'].validate(None)
    
    def test_field_validation(self, partner_model):
        """Test field validation"""
        partner = partner_model()
        
        # Test email field (should accept valid email format)
        email_field = partner._fields['email']
        assert email_field.validate('<EMAIL>') == '<EMAIL>'
        
        # Test boolean field
        is_company_field = partner._fields['is_company']
        assert is_company_field.validate(True) is True
        assert is_company_field.validate(False) is False


class TestResPartnerCategory:
    """Test res.partner.category model"""
    
    @pytest.fixture
    async def category_model(self):
        """Create a category model instance for testing"""
        from addons.base.models.res_partner import ResPartnerCategory
        return ResPartnerCategory
    
    def test_category_model_attributes(self, category_model):
        """Test category model has correct attributes"""
        assert category_model._name == 'res.partner.category'
        assert category_model._description == 'Partner Category'
        assert category_model._table == 'res_partner_category'
    
    def test_category_initialization(self, category_model):
        """Test category initialization"""
        category = category_model(name='Customer', color=1)
        
        assert category.name == 'Customer'
        assert category.color == 1
        assert category.active is True  # Default value


class TestResPartnerTitle:
    """Test res.partner.title model"""
    
    @pytest.fixture
    async def title_model(self):
        """Create a title model instance for testing"""
        from addons.base.models.res_partner import ResPartnerTitle
        return ResPartnerTitle
    
    def test_title_model_attributes(self, title_model):
        """Test title model has correct attributes"""
        assert title_model._name == 'res.partner.title'
        assert title_model._description == 'Partner Title'
        assert title_model._table == 'res_partner_title'
    
    def test_title_initialization(self, title_model):
        """Test title initialization"""
        title = title_model(name='Mister', shortcut='Mr.')
        
        assert title.name == 'Mister'
        assert title.shortcut == 'Mr.'


class TestResCountry:
    """Test res.country model"""
    
    @pytest.fixture
    async def country_model(self):
        """Create a country model instance for testing"""
        from addons.base.models.res_partner import ResCountry
        return ResCountry
    
    def test_country_model_attributes(self, country_model):
        """Test country model has correct attributes"""
        assert country_model._name == 'res.country'
        assert country_model._description == 'Country'
        assert country_model._table == 'res_country'
    
    def test_country_initialization(self, country_model):
        """Test country initialization"""
        country = country_model(name='United States', code='US', phone_code=1)
        
        assert country.name == 'United States'
        assert country.code == 'US'
        assert country.phone_code == 1
