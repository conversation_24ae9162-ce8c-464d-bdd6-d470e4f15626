#!/usr/bin/env python3
"""
Test script to verify the res.partner, res.user, res.group implementation

This script tests the basic functionality of the implemented models
without requiring a full database setup.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_model_imports():
    """Test that all models can be imported successfully"""
    print("Testing model imports...")

    try:
        from addons.base.models.res_partner import ResPartner, ResPartnerCategory, ResPartnerTitle, ResCountry, ResCountryState
        from addons.base.models.res_group import ResGroup, IrModuleCategory
        from addons.base.models.res_user import ResUser

        print("✓ All models imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_model_attributes():
    """Test that models have correct attributes"""
    print("\nTesting model attributes...")

    try:
        from addons.base.models.res_partner import ResPartner
        from addons.base.models.res_group import ResGroup
        from addons.base.models.res_user import ResUser

        # Test ResPartner
        assert ResPartner._name == 'res.partner'
        assert ResPartner._table == 'res_partner'
        assert 'name' in ResPartner._fields
        assert 'email' in ResPartner._fields
        assert 'is_company' in ResPartner._fields

        # Test ResGroup
        assert ResGroup._name == 'res.groups'
        assert ResGroup._table == 'res_groups'
        assert 'name' in ResGroup._fields
        assert 'users' in ResGroup._fields

        # Test ResUser
        assert ResUser._name == 'res.users'
        assert ResUser._table == 'res_users'
        assert 'login' in ResUser._fields
        assert 'partner_id' in ResUser._fields

        print("✓ All model attributes correct")
        return True
    except Exception as e:
        print(f"✗ Attribute error: {e}")
        return False

def test_model_instantiation():
    """Test that models can be instantiated"""
    print("\nTesting model instantiation...")

    try:
        from addons.base.models.res_partner import ResPartner
        from addons.base.models.res_group import ResGroup
        from addons.base.models.res_user import ResUser

        # Test partner creation
        partner = ResPartner(
            name='Test Partner',
            email='<EMAIL>',
            is_company=False
        )
        assert partner.name == 'Test Partner'
        assert partner.email == '<EMAIL>'
        assert partner.is_company is False

        # Test group creation
        group = ResGroup(
            name='Test Group',
            comment='Test group for verification'
        )
        assert group.name == 'Test Group'
        assert group.comment == 'Test group for verification'

        # Test user creation
        user = ResUser(
            login='testuser'
        )
        user.partner_id = 'test_partner_id'  # Set after creation
        assert user.login == 'testuser'
        assert user.partner_id == 'test_partner_id'

        print("✓ All models can be instantiated")
        return True
    except Exception as e:
        print(f"✗ Instantiation error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_field_validation():
    """Test field validation"""
    print("\nTesting field validation...")

    try:
        from addons.base.models.res_partner import ResPartner
        from addons.base.models.res_user import ResUser
        from erp.fields import FieldValidationError

        # Test required field validation
        partner = ResPartner()
        try:
            partner._fields['name'].validate(None)
            print("✗ Required field validation failed")
            return False
        except FieldValidationError:
            pass  # Expected

        # Test valid field validation
        name_field = partner._fields['name']
        validated_name = name_field.validate('Valid Name')
        assert validated_name == 'Valid Name'

        print("✓ Field validation working")
        return True
    except Exception as e:
        print(f"✗ Validation error: {e}")
        return False

def test_computed_fields():
    """Test computed field methods"""
    print("\nTesting computed fields...")

    try:
        from addons.base.models.res_partner import ResPartner
        from addons.base.models.res_group import ResGroup

        # Test partner company type computation
        partner = ResPartner(name='Test Partner', is_company=False)
        partner._compute_company_type()
        assert partner.company_type == 'person'

        partner.is_company = True
        partner._compute_company_type()
        assert partner.company_type == 'company'

        # Test display name computation
        partner = ResPartner(name='John Doe')
        partner._compute_display_name()
        assert partner.display_name == 'John Doe'

        # Test group full name computation
        group = ResGroup(name='Test Group')
        group._compute_full_name()
        assert group.full_name == 'Test Group'

        print("✓ Computed fields working")
        return True
    except Exception as e:
        print(f"✗ Computed field error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_authentication():
    """Test user authentication methods"""
    print("\nTesting user authentication...")

    try:
        from addons.base.models.res_user import ResUser

        user = ResUser(login='testuser')

        # Test password encryption
        password = 'testpassword123'
        encrypted = user._encrypt_password(password)
        assert encrypted != password
        assert encrypted.startswith('pbkdf2_sha256$')

        # Test password verification
        assert user._verify_password(password, encrypted) is True
        assert user._verify_password('wrongpassword', encrypted) is False

        # Test API key generation
        api_key = user._generate_api_key()
        assert api_key is not None
        assert len(api_key) > 20

        print("✓ User authentication methods working")
        return True
    except Exception as e:
        print(f"✗ Authentication error: {e}")
        return False

def test_xml_data_parsing():
    """Test XML data parsing"""
    print("\nTesting XML data parsing...")

    try:
        from erp.data import XMLDataParser

        sample_xml = '''<?xml version="1.0" encoding="utf-8"?>
<data>
    <record id="test_partner" model="res.partner">
        <field name="name">Test Partner</field>
        <field name="email"><EMAIL></field>
        <field name="is_company" eval="False"/>
    </record>
</data>'''

        parser = XMLDataParser()
        records = parser.parse_content(sample_xml)

        assert len(records) == 1
        record = records[0]
        assert record['model'] == 'res.partner'
        assert record['xml_id'] == 'test_partner'
        assert record['values']['name']['value'] == 'Test Partner'
        assert record['values']['is_company']['type'] == 'eval'

        print("✓ XML data parsing working")
        return True
    except Exception as e:
        print(f"✗ XML parsing error: {e}")
        return False

def test_data_files_exist():
    """Test that data files exist and are valid"""
    print("\nTesting data files...")

    try:
        data_files = [
            'addons/base/data/res_groups_data.xml',
            'addons/base/data/res_partner_data.xml',
            'addons/base/data/res_users_data.xml'
        ]

        for file_path in data_files:
            if not os.path.exists(file_path):
                print(f"✗ Data file missing: {file_path}")
                return False

            # Test that file can be parsed
            from erp.data import XMLDataParser
            parser = XMLDataParser()

            try:
                records = parser.parse_file(file_path)
                print(f"  ✓ {file_path}: {len(records)} records")
            except Exception as e:
                print(f"  ✗ {file_path}: Parse error - {e}")
                return False

        print("✓ All data files exist and are valid")
        return True
    except Exception as e:
        print(f"✗ Data file error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing res.partner, res.user, res.group implementation")
    print("=" * 60)

    tests = [
        test_model_imports,
        test_model_attributes,
        test_model_instantiation,
        test_field_validation,
        test_computed_fields,
        test_user_authentication,
        test_xml_data_parsing,
        test_data_files_exist
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            result = test()
            if result:
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")

    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Implementation is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
